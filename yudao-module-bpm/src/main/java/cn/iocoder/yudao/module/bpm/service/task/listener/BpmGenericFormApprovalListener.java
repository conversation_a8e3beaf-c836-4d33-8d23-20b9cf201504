package cn.iocoder.yudao.module.bpm.service.task.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import cn.iocoder.yudao.module.bpm.enums.task.BpmTaskStatusEnum;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.enums.BpmnVariableConstants;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.FlowableUtils;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import jakarta.annotation.Resource;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.el.FixedValue;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.task.api.Task;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 通用表单审批监听器
 * 审批通过后，自动创建台账流程实例（使用Flowable历史变量作为台账数据）
 *
 * 使用方式：
 * 1. 在BPMN流程设计器中配置ExecutionListener
 * 2. 监听器类型：delegateExpression
 * 3. 监听器表达式：${bpmGenericFormApprovalListener}
 * 4. 监听事件：end（流程结束时触发）
 * 5. 扩展字段配置：
 *    字段名：listenerConfig
 *    字段值：{"ledgerProcessKey":"台账流程定义Key"}
 *
 * 示例配置：
 * 扩展字段值：{"ledgerProcessKey":"contract_ledger_process"}
 *
 * 工作原理：
 * 1. 监听表单审批流程结束事件
 * 2. 检查流程是否审批通过
 * 3. 获取原流程的表单数据
 * 4. 启动台账流程实例，将表单数据作为流程变量传入
 * 5. 台账流程实例状态直接设置为通过，用于数据存储和查询
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BpmGenericFormApprovalListener implements ExecutionListener {

    @Resource
    private HistoryService historyService;

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private TaskService taskService;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private DeptApi deptApi;

    @Setter
    private FixedValue listenerConfig;

    // 添加构造函数来确认Bean是否被正确创建
    public BpmGenericFormApprovalListener() {
        log.info("BpmGenericFormApprovalListener Bean 已创建");
    }

    /**
     * 监听器配置参数
     */
    public static class ListenerConfig {
        private String ledgerProcessKey;
        private String businessType;

        public String getLedgerProcessKey() { return ledgerProcessKey; }
        public void setLedgerProcessKey(String ledgerProcessKey) { this.ledgerProcessKey = ledgerProcessKey; }
        public String getBusinessType() { return businessType; }
        public void setBusinessType(String businessType) { this.businessType = businessType; }

        @Override
        public String toString() {
            return "ListenerConfig{" +
                    "ledgerProcessKey='" + ledgerProcessKey + '\'' +
                    ", businessType='" + businessType + '\'' +
                    '}';
        }
    }

    @Override
    public void notify(DelegateExecution execution) {
        log.info("=== BpmGenericFormApprovalListener.notify() 被调用 ===");
        log.info("流程实例ID: {}", execution.getProcessInstanceId());
        log.info("当前活动ID: {}", execution.getCurrentActivityId());
        log.info("事件名称: {}", execution.getEventName());

        try {
            // 1. 解析监听器配置
            ListenerConfig config = parseConfig();
            log.info("解析监听器配置结果: {}", config);
            if (config == null) {
                log.warn("监听器配置为空，跳过处理");
                return;
            }
            if (StrUtil.isBlank(config.getLedgerProcessKey())) {
                log.warn("台账流程Key为空，跳过处理。配置: {}", config);
                return;
            }
            log.info("监听器配置解析成功，台账流程Key: {}, 业务类型: {}", config.getLedgerProcessKey(), config.getBusinessType());

            // 2. 获取流程实例的历史数据
            log.info("开始获取流程实例历史数据...");
            HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(execution.getProcessInstanceId())
                    .includeProcessVariables()
                    .singleResult();

            if (processInstance == null) {
                log.warn("未找到流程实例: {}", execution.getProcessInstanceId());
                return;
            }
            log.info("成功获取流程实例: {}, 开始时间: {}, 结束时间: {}",
                    processInstance.getId(), processInstance.getStartTime(), processInstance.getEndTime());

            // 3. 检查审批状态
            log.info("检查流程审批状态...");

            Map<String, Object> executionVariables = execution.getVariables();
            Map<String, Object> processVariables = processInstance.getProcessVariables();

            // 优先检查状态变量
            Object statusVar = processVariables.get("status");
            Object executionStatusVar = executionVariables.get("status");
            Object processStatusVar = processVariables.get("PROCESS_STATUS");
            Object executionProcessStatusVar = executionVariables.get("PROCESS_STATUS");

            log.info("状态检查 - status: {}, PROCESS_STATUS: {}", statusVar, processStatusVar);

            // 第一步：立即检查是否被拒绝或取消，如果是则直接返回
            if (statusVar != null && BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(statusVar)) {
                log.warn("流程实例 {} 被拒绝，status: {}, 跳过处理", execution.getProcessInstanceId(), statusVar);
                return;
            }
            if (statusVar != null && BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(statusVar)) {
                log.warn("流程实例 {} 被取消，status: {}, 跳过处理", execution.getProcessInstanceId(), statusVar);
                return;
            }
            if (processStatusVar != null && BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(processStatusVar)) {
                log.warn("流程实例 {} 被拒绝，PROCESS_STATUS: {}, 跳过处理", execution.getProcessInstanceId(), processStatusVar);
                return;
            }
            if (processStatusVar != null && BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(processStatusVar)) {
                log.warn("流程实例 {} 被取消，PROCESS_STATUS: {}, 跳过处理", execution.getProcessInstanceId(), processStatusVar);
                return;
            }
            if (executionStatusVar != null && BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(executionStatusVar)) {
                log.warn("流程实例 {} 被拒绝，执行变量status: {}, 跳过处理", execution.getProcessInstanceId(), executionStatusVar);
                return;
            }
            if (executionStatusVar != null && BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(executionStatusVar)) {
                log.warn("流程实例 {} 被取消，执行变量status: {}, 跳过处理", execution.getProcessInstanceId(), executionStatusVar);
                return;
            }
            if (executionProcessStatusVar != null && BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(executionProcessStatusVar)) {
                log.warn("流程实例 {} 被拒绝，执行变量PROCESS_STATUS: {}, 跳过处理", execution.getProcessInstanceId(), executionProcessStatusVar);
                return;
            }
            if (executionProcessStatusVar != null && BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(executionProcessStatusVar)) {
                log.warn("流程实例 {} 被取消，执行变量PROCESS_STATUS: {}, 跳过处理", execution.getProcessInstanceId(), executionProcessStatusVar);
                return;
            }

            // 第二步：检查是否明确审批通过
            boolean isApproved = false;

            if (statusVar != null && BpmProcessInstanceStatusEnum.APPROVE.getStatus().equals(statusVar)) {
                isApproved = true;
                log.info("通过流程变量status确认审批通过");
            } else if (executionStatusVar != null && BpmProcessInstanceStatusEnum.APPROVE.getStatus().equals(executionStatusVar)) {
                isApproved = true;
                log.info("通过执行变量status确认审批通过");
            } else if (processStatusVar != null && BpmProcessInstanceStatusEnum.APPROVE.getStatus().equals(processStatusVar)) {
                isApproved = true;
                log.info("通过流程变量PROCESS_STATUS确认审批通过");
            } else if (executionProcessStatusVar != null && BpmProcessInstanceStatusEnum.APPROVE.getStatus().equals(executionProcessStatusVar)) {
                isApproved = true;
                log.info("通过执行变量PROCESS_STATUS确认审批通过");
            }

            // 第三步：对于end事件，如果没有明确的拒绝/取消状态，视为审核通过
            if (!isApproved && "end".equals(execution.getEventName())) {
                isApproved = true;
                log.info("end事件触发且无拒绝/取消状态，视为审核通过");
            }

            if (!isApproved) {
                log.warn("流程实例 {} 未确认为审核通过状态，跳过处理。status={}, PROCESS_STATUS={}",
                        execution.getProcessInstanceId(), statusVar, processStatusVar);
                return;
            }

            log.info("确认流程审批通过，继续处理...");

            // 4. 获取表单数据
            log.info("获取表单数据...");
            Map<String, Object> formVariables = FlowableUtils.getProcessInstanceFormVariable(processInstance);
            log.info("表单数据: {}", formVariables);
            if (CollUtil.isEmpty(formVariables)) {
                log.warn("流程实例 {} 没有表单数据", execution.getProcessInstanceId());
                return;
            }

            // 5. 创建台账流程实例
            log.info("开始创建台账流程实例...");
            createLedgerProcessInstance(config.getLedgerProcessKey(), formVariables, processInstance, config);

            log.info("表单审批监听器处理完成，流程实例: {}", execution.getProcessInstanceId());

        } catch (Exception e) {
            log.error("表单审批监听器处理失败，流程实例: {}", execution.getProcessInstanceId(), e);
        }
    }

    /**
     * 通过JSON字符串配置处理审批
     * UEL表达式调用：${bpmGenericFormApprovalListener.processWithJsonConfig(execution, '{"ledgerProcessKey":"_CustomerFileLedger","businessType":"contract"}')}
     */
    public String processWithJsonConfig(Object executionObj, String jsonConfig) {
        try {
            if (!(executionObj instanceof DelegateExecution)) {
                log.warn("执行对象类型不正确，跳过处理");
                return "SKIPPED";
            }

            DelegateExecution execution = (DelegateExecution) executionObj;
            log.info("通过JSON配置处理表单审批，流程实例: {}, 配置: {}", execution.getProcessInstanceId(), jsonConfig);

            // 解析JSON配置
            ListenerConfig config = JsonUtils.parseObject(jsonConfig, ListenerConfig.class);
            if (config == null || StrUtil.isBlank(config.getLedgerProcessKey())) {
                log.warn("JSON配置解析失败或台账流程Key为空，跳过处理");
                return "CONFIG_ERROR";
            }

            // 处理审批逻辑
            processApprovalInternal(execution, config);
            return "SUCCESS";

        } catch (Exception e) {
            log.error("通过JSON配置处理表单审批失败，配置: {}", jsonConfig, e);
            return "ERROR";
        }
    }

    /**
     * 通过参数处理审批
     * UEL表达式调用：${bpmGenericFormApprovalListener.processWithParams(execution, '_CustomerFileLedger', 'contract')}
     */
    public String processWithParams(Object executionObj, String ledgerProcessKey, String businessType) {
        try {
            if (!(executionObj instanceof DelegateExecution)) {
                log.warn("执行对象类型不正确，跳过处理");
                return "SKIPPED";
            }

            DelegateExecution execution = (DelegateExecution) executionObj;
            log.info("通过参数处理表单审批，流程实例: {}, 台账流程Key: {}, 业务类型: {}",
                    execution.getProcessInstanceId(), ledgerProcessKey, businessType);

            if (StrUtil.isBlank(ledgerProcessKey)) {
                log.warn("台账流程Key为空，跳过处理");
                return "PARAM_ERROR";
            }

            // 创建配置对象
            ListenerConfig config = new ListenerConfig();
            config.setLedgerProcessKey(ledgerProcessKey);
            config.setBusinessType(businessType);

            // 处理审批逻辑
            processApprovalInternal(execution, config);
            return "SUCCESS";

        } catch (Exception e) {
            log.error("通过参数处理表单审批失败，参数: {}, {}", ledgerProcessKey, businessType, e);
            return "ERROR";
        }
    }

    /**
     * 解析监听器配置
     */
    private ListenerConfig parseConfig() {
        log.info("开始解析监听器配置...");
        if (listenerConfig == null) {
            log.warn("listenerConfig为null");
            return null;
        }
        String expressionText = listenerConfig.getExpressionText();
        log.info("获取到的配置文本: {}", expressionText);
        if (StrUtil.isBlank(expressionText)) {
            log.warn("配置文本为空");
            return null;
        }
        try {
            ListenerConfig config = JsonUtils.parseObject(expressionText, ListenerConfig.class);
            log.info("JSON解析成功: {}", config);
            return config;
        } catch (Exception e) {
            log.error("JSON解析失败: {}", expressionText, e);
            return null;
        }
    }

    /**
     * 内部处理审批逻辑
     */
    private void processApprovalInternal(DelegateExecution execution, ListenerConfig config) {
        // 获取流程实例的历史数据
        HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(execution.getProcessInstanceId())
                .includeProcessVariables()
                .singleResult();

        if (processInstance == null) {
            log.warn("未找到流程实例: {}", execution.getProcessInstanceId());
            return;
        }

        // 检查审批状态，确保只有审批通过才同步台账数据
        Map<String, Object> executionVariables = execution.getVariables();
        Map<String, Object> processVariables = processInstance.getProcessVariables();

        // 优先检查状态变量
        Object statusVar = processVariables.get("status");
        Object executionStatusVar = executionVariables.get("status");
        Object processStatusVar = processVariables.get("PROCESS_STATUS");
        Object executionProcessStatusVar = executionVariables.get("PROCESS_STATUS");

        log.info("状态检查 - status: {}, PROCESS_STATUS: {}", statusVar, processStatusVar);

        // 第一步：立即检查是否被拒绝或取消，如果是则直接返回
        if (statusVar != null && BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(statusVar)) {
            log.warn("流程实例 {} 被拒绝，status: {}, 跳过处理", execution.getProcessInstanceId(), statusVar);
            return;
        }
        if (statusVar != null && BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(statusVar)) {
            log.warn("流程实例 {} 被取消，status: {}, 跳过处理", execution.getProcessInstanceId(), statusVar);
            return;
        }
        if (processStatusVar != null && BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(processStatusVar)) {
            log.warn("流程实例 {} 被拒绝，PROCESS_STATUS: {}, 跳过处理", execution.getProcessInstanceId(), processStatusVar);
            return;
        }
        if (processStatusVar != null && BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(processStatusVar)) {
            log.warn("流程实例 {} 被取消，PROCESS_STATUS: {}, 跳过处理", execution.getProcessInstanceId(), processStatusVar);
            return;
        }
        if (executionStatusVar != null && BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(executionStatusVar)) {
            log.warn("流程实例 {} 被拒绝，执行变量status: {}, 跳过处理", execution.getProcessInstanceId(), executionStatusVar);
            return;
        }
        if (executionStatusVar != null && BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(executionStatusVar)) {
            log.warn("流程实例 {} 被取消，执行变量status: {}, 跳过处理", execution.getProcessInstanceId(), executionStatusVar);
            return;
        }
        if (executionProcessStatusVar != null && BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(executionProcessStatusVar)) {
            log.warn("流程实例 {} 被拒绝，执行变量PROCESS_STATUS: {}, 跳过处理", execution.getProcessInstanceId(), executionProcessStatusVar);
            return;
        }
        if (executionProcessStatusVar != null && BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(executionProcessStatusVar)) {
            log.warn("流程实例 {} 被取消，执行变量PROCESS_STATUS: {}, 跳过处理", execution.getProcessInstanceId(), executionProcessStatusVar);
            return;
        }

        // 第二步：检查是否明确审批通过
        boolean isApproved = false;

        if (statusVar != null && BpmProcessInstanceStatusEnum.APPROVE.getStatus().equals(statusVar)) {
            isApproved = true;
            log.info("通过流程变量status确认审批通过");
        } else if (executionStatusVar != null && BpmProcessInstanceStatusEnum.APPROVE.getStatus().equals(executionStatusVar)) {
            isApproved = true;
            log.info("通过执行变量status确认审批通过");
        } else if (processStatusVar != null && BpmProcessInstanceStatusEnum.APPROVE.getStatus().equals(processStatusVar)) {
            isApproved = true;
            log.info("通过流程变量PROCESS_STATUS确认审批通过");
        } else if (executionProcessStatusVar != null && BpmProcessInstanceStatusEnum.APPROVE.getStatus().equals(executionProcessStatusVar)) {
            isApproved = true;
            log.info("通过执行变量PROCESS_STATUS确认审批通过");
        }

        if (!isApproved) {
            log.warn("流程实例 {} 未确认为审核通过状态，跳过处理。status={}, PROCESS_STATUS={}",
                    execution.getProcessInstanceId(), statusVar, processStatusVar);
            return;
        }

        log.info("确认流程审批通过，继续处理...");

        // 获取表单数据
        Map<String, Object> formVariables = FlowableUtils.getProcessInstanceFormVariable(processInstance);
        if (CollUtil.isEmpty(formVariables)) {
            log.warn("流程实例 {} 没有表单数据", execution.getProcessInstanceId());
            return;
        }

        // 创建台账流程实例
        createLedgerProcessInstance(config.getLedgerProcessKey(), formVariables, processInstance, config);

        log.info("表单审批监听器处理完成，流程实例: {}, 业务类型: {}",
                execution.getProcessInstanceId(), config.getBusinessType());
    }

    /**
     * 创建台账流程实例
     */
    private void createLedgerProcessInstance(String ledgerProcessKey, Map<String, Object> formVariables, HistoricProcessInstance originalProcessInstance, ListenerConfig config) {
        try {
            // 检查流程定义是否存在
            log.info("检查台账流程定义是否存在: {}", ledgerProcessKey);

            // 查询所有可用的流程定义
            List<ProcessDefinition> allProcessDefs = repositoryService
                    .createProcessDefinitionQuery()
                    .list();

            // 检查目标流程定义
            ProcessDefinition targetProcessDef = repositoryService
                    .createProcessDefinitionQuery()
                    .processDefinitionKey(ledgerProcessKey)
                    .latestVersion()
                    .singleResult();

            if (targetProcessDef == null) {
                log.warn("直接查找流程定义失败: {}，尝试通过模型查找最新部署的流程定义", ledgerProcessKey);

                // 方法1：查找模型对应的最新部署
                targetProcessDef = findProcessDefinitionByModel(ledgerProcessKey);

                if (targetProcessDef == null) {
                    // 方法2：查找最新部署的流程定义（按时间排序）
                    targetProcessDef = findLatestDeployedProcessDefinition();
                }

                if (targetProcessDef == null) {
                    log.error("无法找到任何可用的台账流程定义，请检查流程部署状态");
                    return;
                }

                log.info("通过备用方法找到流程定义: Key={}, Name={}, Version={}, ID={}",
                        targetProcessDef.getKey(), targetProcessDef.getName(), targetProcessDef.getVersion(), targetProcessDef.getId());
            } else {
                log.info("找到台账流程定义: Key={}, Name={}, Version={}, ID={}",
                        targetProcessDef.getKey(), targetProcessDef.getName(), targetProcessDef.getVersion(), targetProcessDef.getId());
            }
            // 准备台账流程变量
            Map<String, Object> ledgerVariables = new HashMap<>(formVariables);

            // 添加系统变量
            ledgerVariables.put("original_process_instance_id", originalProcessInstance.getId());
            ledgerVariables.put("original_process_key", originalProcessInstance.getProcessDefinitionKey());
            ledgerVariables.put("original_process_name", originalProcessInstance.getName());
            ledgerVariables.put("business_type", config.getBusinessType());

            // 设置台账流程状态为成功（已完成）
            ledgerVariables.put("status", BpmProcessInstanceStatusEnum.APPROVE.getStatus());
            ledgerVariables.put(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS, BpmProcessInstanceStatusEnum.APPROVE.getStatus());

            // 设置时间和创建者信息
            ledgerVariables.put("create_time", LocalDateTime.now());
            ledgerVariables.put("creator", originalProcessInstance.getStartUserId());
            ledgerVariables.put("PROCESS_START_USER_ID", originalProcessInstance.getStartUserId());

            // 添加台账审批历史记录
            List<Map<String, Object>> approvalHistory = createLedgerApprovalHistory(originalProcessInstance);
            ledgerVariables.put("approval_history", approvalHistory);
            log.info("添加台账审批历史记录: 状态=已完成, 审批建议=自动完成");

            log.info("台账流程变量: {}", ledgerVariables);

            // 启动台账流程实例
            FlowableUtils.setAuthenticatedUserId(Long.parseLong(originalProcessInstance.getStartUserId()));

            // 使用找到的流程定义ID启动流程实例
            String processDefinitionId = targetProcessDef.getId();
            String actualProcessKey = targetProcessDef.getKey();
            log.info("使用流程定义ID启动台账流程: ID={}, Key={}", processDefinitionId, actualProcessKey);

            org.flowable.engine.runtime.ProcessInstance ledgerProcessInstance = runtimeService.startProcessInstanceById(
                    processDefinitionId,
                    originalProcessInstance.getId() + "_ledger", // 使用原流程ID作为业务Key的一部分
                    ledgerVariables
            );

            // 设置台账流程实例的名称（使用台账模型的名称）
            String ledgerProcessName = targetProcessDef.getName();
            if (ledgerProcessName != null) {
                runtimeService.setProcessInstanceName(ledgerProcessInstance.getId(), ledgerProcessName);
                log.info("设置台账流程名称: {}", ledgerProcessName);
            }

            // 立即完成台账流程（设置为已完成状态）
            // 由于台账流程只是用来记录数据，不需要实际的审批流程
            try {
                // 查找台账流程中的活动任务并自动完成
                List<Task> tasks = taskService.createTaskQuery()
                        .processInstanceId(ledgerProcessInstance.getId())
                        .list();

                for (Task task : tasks) {
                    log.info("自动完成台账流程任务: {}", task.getName());
                    // 先设置任务状态为审批通过
                    taskService.setVariableLocal(task.getId(), BpmnVariableConstants.TASK_VARIABLE_STATUS, BpmTaskStatusEnum.APPROVE.getStatus());
                    // 然后完成任务
                    taskService.complete(task.getId());
                }

                log.info("台账流程已自动完成，共完成 {} 个任务", tasks.size());
            } catch (Exception e) {
                log.warn("自动完成台账流程失败，但流程实例已创建: {}", e.getMessage());
            }

            log.info("成功创建台账流程实例，原流程: {}, 台账流程: {}, 流程名称: {}, 使用的流程定义ID: {}, Key: {}",
                    originalProcessInstance.getId(), ledgerProcessInstance.getId(), ledgerProcessName, processDefinitionId, actualProcessKey);

        } catch (Exception e) {
            log.error("创建台账流程实例失败，台账流程Key: {}, 原流程: {}",
                    ledgerProcessKey, originalProcessInstance.getId(), e);
            throw new RuntimeException("创建台账流程实例失败", e);
        }
    }

    /**
     * 通过模型查找对应的流程定义
     */
    private ProcessDefinition findProcessDefinitionByModel(String modelKey) {
        try {
            log.info("通过模型Key查找流程定义: {}", modelKey);

            // 查找模型记录
            org.flowable.engine.repository.Model model = repositoryService
                    .createModelQuery()
                    .modelKey(modelKey)
                    .latestVersion()
                    .singleResult();

            if (model == null) {
                log.warn("未找到模型: {}", modelKey);
                return null;
            }

            log.info("找到模型: Key={}, Name={}, DeploymentId={}",
                    model.getKey(), model.getName(), model.getDeploymentId());

            if (model.getDeploymentId() != null) {
                // 通过部署ID查找流程定义
                List<ProcessDefinition> processDefinitions = repositoryService
                        .createProcessDefinitionQuery()
                        .deploymentId(model.getDeploymentId())
                        .list();

                if (!processDefinitions.isEmpty()) {
                    ProcessDefinition pd = processDefinitions.get(0);
                    log.info("通过模型找到流程定义: Key={}, Name={}, ID={}", pd.getKey(), pd.getName(), pd.getId());
                    return pd;
                }
            }

            return null;
        } catch (Exception e) {
            log.error("通过模型查找流程定义失败", e);
            return null;
        }
    }

    /**
     * 查找最新部署的流程定义
     */
    private ProcessDefinition findLatestDeployedProcessDefinition() {
        try {
            log.info("查找最新部署的流程定义...");

            // 查找最新部署的流程定义（按版本排序）
            List<ProcessDefinition> processDefinitions = repositoryService
                    .createProcessDefinitionQuery()
                    .orderByProcessDefinitionVersion()
                    .desc()
                    .listPage(0, 10); // 获取最新的10个

            log.info("找到 {} 个流程定义", processDefinitions.size());

            for (ProcessDefinition pd : processDefinitions) {
                log.info("候选流程定义: Key={}, Name={}, Version={}, ID={}",
                        pd.getKey(), pd.getName(), pd.getVersion(), pd.getId());

                // 如果流程定义Key包含台账相关关键词，优先选择
                if (pd.getKey().toLowerCase().contains("ledger") ||
                    pd.getKey().toLowerCase().contains("台账") ||
                    pd.getName().toLowerCase().contains("ledger") ||
                    pd.getName().toLowerCase().contains("台账")) {
                    log.info("选择台账相关的流程定义: Key={}, Name={}, ID={}", pd.getKey(), pd.getName(), pd.getId());
                    return pd;
                }
            }

            // 如果没有找到台账相关的，返回最新的流程定义
            if (!processDefinitions.isEmpty()) {
                ProcessDefinition latest = processDefinitions.get(0);
                log.info("选择最新的流程定义: Key={}, Name={}, ID={}", latest.getKey(), latest.getName(), latest.getId());
                return latest;
            }

            return null;
        } catch (Exception e) {
            log.error("查找最新流程定义失败", e);
            return null;
        }
    }

    /**
     * 创建台账审批历史记录
     */
    private List<Map<String, Object>> createLedgerApprovalHistory(HistoricProcessInstance originalProcessInstance) {
        List<Map<String, Object>> approvalHistory = new ArrayList<>();

        try {
            // 获取流程发起人信息
            String startUserId = originalProcessInstance.getStartUserId();
            String assigneeName = "系统";
            String deptName = "";

            if (StrUtil.isNotBlank(startUserId)) {
                try {
                    Long userId = Long.valueOf(startUserId);
                    AdminUserRespDTO user = adminUserApi.getUser(userId);
                    if (user != null) {
                        assigneeName = user.getNickname();
                        if (user.getDeptId() != null) {
                            DeptRespDTO dept = deptApi.getDept(user.getDeptId());
                            if (dept != null) {
                                deptName = dept.getName();
                            }
                        }
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的用户ID: {}", startUserId);
                }
            }

            // 创建台账审批记录
            Map<String, Object> record = new HashMap<>();
            record.put("sequence", 1);
            record.put("assignee", assigneeName);
            record.put("department", deptName);
            record.put("startTime", originalProcessInstance.getStartTime());
            record.put("endTime", LocalDateTime.now());
            record.put("status", "已完成");
            record.put("reason", "自动完成");

            // 计算耗时
            long duration = 0;
            if (originalProcessInstance.getStartTime() != null) {
                duration = System.currentTimeMillis() - originalProcessInstance.getStartTime().getTime();
            }
            record.put("duration", duration);
            record.put("durationText", formatDuration(duration));

            approvalHistory.add(record);

            log.info("创建台账审批记录: 审批人={}, 部门={}, 状态=已完成, 审批建议=自动完成",
                     assigneeName, deptName);

        } catch (Exception e) {
            log.error("创建台账审批历史记录失败，流程实例: {}", originalProcessInstance.getId(), e);
        }

        return approvalHistory;
    }

    /**
     * 格式化持续时间
     */
    private String formatDuration(long durationInMillis) {
        if (durationInMillis <= 0) {
            return "0 秒";
        }

        long seconds = durationInMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        if (days > 0) {
            return days + " 天 " + (hours % 24) + " 小时";
        } else if (hours > 0) {
            return hours + " 小时 " + (minutes % 60) + " 分钟";
        } else if (minutes > 0) {
            return minutes + " 分钟 " + (seconds % 60) + " 秒";
        } else {
            return seconds + " 秒";
        }
    }

}
