# 台账数据查询状态修复测试

## 问题描述
查询台账数据时，添加 `status=2` 参数就查不到数据，不添加这个 status 筛选条件就能查到数据。

## 问题根因分析

### 关键发现：为什么 status=1 能查到 status=2 的数据？
通过分析查询逻辑发现：
- `status=1` (RUNNING) 的查询条件是：`processInstanceQuery.unfinished()` - 查询**未完成**的流程实例
- `status=2` (APPROVE) 的查询条件是：`processInstanceQuery.finished().variableValueEquals(PROCESS_STATUS, 2)` - 查询**已完成且状态为通过**的流程实例

**真正的问题**：台账流程实例虽然所有任务都被自动完成了，但是**流程实例本身没有被标记为已完成**（endTime 为 null），所以：
- 能被 `status=1` (未完成) 查询到
- 不能被 `status=2` (已完成) 查询到

### 根本原因
1. **台账流程实例状态设置问题**：
   - 原来台账流程实例创建时直接设置状态为 `APPROVE(2)`
   - 但是在 `processProcessInstanceCompleted` 方法中，只有当状态是 `RUNNING(1)` 时才会设置为 `APPROVE(2)`
   - 导致台账流程实例的 `PROCESS_STATUS` 变量可能没有被正确设置

2. **台账流程实例完成状态问题**：
   - 台账流程只完成了所有任务，但流程实例本身没有正确结束
   - 导致流程实例在数据库中仍被标记为"未完成"状态

3. **查询逻辑要求**：
   - 当 `status=2` 时，查询条件是：`processInstanceQuery.finished().variableValueEquals(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS, BpmProcessInstanceStatusEnum.APPROVE.getStatus())`
   - 需要流程实例既是 `finished`（已完成），又要有正确的 `PROCESS_STATUS` 变量值

## 修复方案
修改 `BpmGenericFormApprovalListener.createLedgerProcessInstance()` 方法：

1. **初始状态设置为 RUNNING**：
   ```java
   // 设置台账流程状态为运行中，完成任务后会自动设置为审批通过
   // 这样可以确保 processProcessInstanceCompleted 方法正确处理状态变更
   ledgerVariables.put("status", BpmProcessInstanceStatusEnum.RUNNING.getStatus());
   ledgerVariables.put(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS, BpmProcessInstanceStatusEnum.RUNNING.getStatus());
   ```

2. **确保流程实例正确完成**：
   ```java
   // 检查流程实例是否已经完成
   org.flowable.engine.runtime.ProcessInstance runningInstance = runtimeService.createProcessInstanceQuery()
           .processInstanceId(ledgerProcessInstance.getId())
           .singleResult();

   if (runningInstance != null) {
       // 如果流程实例还在运行，强制结束它
       log.info("台账流程实例仍在运行，强制结束: {}", ledgerProcessInstance.getId());
       runtimeService.deleteProcessInstance(ledgerProcessInstance.getId(), "台账流程自动完成");
   }
   ```

3. **自动完成任务后状态自动变更**：
   - 当台账流程的所有任务完成后，会触发 `processProcessInstanceCompleted` 方法
   - 该方法会检查状态是否为 `RUNNING(1)`，如果是则设置为 `APPROVE(2)`
   - 强制结束流程实例确保其被标记为已完成状态
   - 这样确保了 `PROCESS_STATUS` 变量被正确设置，且流程实例状态为已完成

## 测试步骤

### 1. 重新部署应用
确保修改后的代码已经部署到测试环境。

### 2. 创建新的合同台账数据
触发合同审批流程，让监听器创建新的台账流程实例。

### 3. 测试查询
使用以下 curl 命令测试：

```bash
# 测试1：不带 status 参数（应该能查到数据）
curl 'http://223.99.170.187:48080/admin-api/bpm/process-instance/get-form-data-page?pageNo=1&pageSize=10&processDefinitionKey=_ContractLedger&processInstanceIds=' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Authorization: Bearer 7cea179144b04612870916d2f3bcc78d' \
  -H 'tenant-id: 1'

# 测试2：带 status=2 参数（修复后应该能查到数据）
curl 'http://223.99.170.187:48080/admin-api/bpm/process-instance/get-form-data-page?pageNo=1&pageSize=10&status=2&processDefinitionKey=_ContractLedger&processInstanceIds=' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Authorization: Bearer 7cea179144b04612870916d2f3bcc78d' \
  -H 'tenant-id: 1'
```

### 4. 验证结果
- 测试1和测试2都应该返回相同的台账数据
- 返回的数据中 `status` 字段应该为 `2`（审批通过）

## 预期结果
修复后，带 `status=2` 参数的查询应该能正常返回台账数据，解决查询不到的问题。

## 注意事项
1. 这个修复只影响新创建的台账流程实例
2. 对于已经存在的台账流程实例，如果状态有问题，可能需要手动修复数据库中的流程变量
3. 建议在生产环境部署前，先在测试环境充分验证

## 相关文件
- `yudao-module-bpm/src/main/java/cn/iocoder/yudao/module/bpm/service/task/listener/BpmGenericFormApprovalListener.java`
- `yudao-module-bpm/src/main/java/cn/iocoder/yudao/module/bpm/service/task/BpmProcessInstanceServiceImpl.java`
